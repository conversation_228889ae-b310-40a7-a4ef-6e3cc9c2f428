package com.compress.my.ui.components

import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.InsertDriveFile
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.compress.my.utils.FileType

@Composable
fun FilePickerDialog(
    onDismiss: () -> Unit,
    onFilesSelected: (List<Uri>) -> Unit,
    onDirectorySelected: (String) -> Unit,
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("File Types", "Directory")
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Select Files",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Tab Row
                TabRow(selectedTabIndex = selectedTab) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTab == index,
                            onClick = { selectedTab = index },
                            text = { Text(title) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Tab Content
                when (selectedTab) {
                    0 -> FileTypeSelectionTab(
                        onFilesSelected = onFilesSelected,
                        onDismiss = onDismiss
                    )
                    1 -> DirectorySelectionTab(
                        onDirectorySelected = onDirectorySelected,
                        onDismiss = onDismiss
                    )
                }
            }
        }
    }
}

@Composable
private fun FileTypeSelectionTab(
    onFilesSelected: (List<Uri>) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedFileTypes by remember { mutableStateOf(setOf<FileType>()) }

    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenMultipleDocuments()
    ) { uris ->
        if (uris.isNotEmpty()) {
            Log.d("FilePicker", "Selected files: ${uris.map { it.toString() }}")
            onFilesSelected(uris)
            onDismiss()
        }
    }
    
    Column {
        // File Types
        Text(
            text = "File Types",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyColumn(
            modifier = Modifier.height(200.dp)
        ) {
            items(FileType.values()) { fileType ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = selectedFileTypes.contains(fileType),
                            onClick = {
                                selectedFileTypes = if (selectedFileTypes.contains(fileType)) {
                                    selectedFileTypes - fileType
                                } else {
                                    selectedFileTypes + fileType
                                }
                            }
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = selectedFileTypes.contains(fileType),
                        onCheckedChange = { checked ->
                            selectedFileTypes = if (checked) {
                                selectedFileTypes + fileType
                            } else {
                                selectedFileTypes - fileType
                            }
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.InsertDriveFile,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(fileType.displayName)
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Action Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
            Spacer(modifier = Modifier.width(8.dp))
            Button(
                onClick = {
                    val mimeTypes = selectedFileTypes.flatMap { it.mimeTypes }.toTypedArray()
                    if (mimeTypes.isNotEmpty()) {
                        filePickerLauncher.launch(mimeTypes)
                    }
                },
                enabled = selectedFileTypes.isNotEmpty()
            ) {
                Text("Select Files")
            }
        }
    }
}

@Composable
private fun DirectorySelectionTab(
    onDirectorySelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    Column {
        Text(
            text = "Browse and select a directory:",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(16.dp))

        DirectoryBrowser(
            onDirectorySelected = { path ->
                onDirectorySelected(path)
                onDismiss()
            },
            modifier = Modifier.height(400.dp)
        )
    }
}



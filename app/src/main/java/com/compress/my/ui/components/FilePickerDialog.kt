package com.compress.my.ui.components

import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.InsertDriveFile
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.compress.my.utils.FileType

@Composable
fun FilePickerDialog(
    onDismiss: () -> Unit,
    onFilesSelected: (List<Uri>) -> Unit,
    onDirectorySelected: (Uri) -> Unit,
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("File Types", "Directory")
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Select Files",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Tab Row
                TabRow(selectedTabIndex = selectedTab) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTab == index,
                            onClick = { selectedTab = index },
                            text = { Text(title) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Tab Content
                when (selectedTab) {
                    0 -> FileTypeSelectionTab(
                        onFilesSelected = onFilesSelected,
                        onDismiss = onDismiss
                    )
                    1 -> DirectorySelectionTab(
                        onDirectorySelected = onDirectorySelected,
                        onDismiss = onDismiss
                    )
                }
            }
        }
    }
}

@Composable
private fun FileTypeSelectionTab(
    onFilesSelected: (List<Uri>) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedFileTypes by remember { mutableStateOf(setOf<FileType>()) }
    var selectionMode by remember { mutableStateOf(SelectionMode.MULTIPLE) }
    
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenMultipleDocuments()
    ) { uris ->
        if (uris.isNotEmpty()) {
            Log.d("FilePicker", "Selected files: ${uris.map { it.toString() }}")
            onFilesSelected(uris)
            onDismiss()
        }
    }
    
    val singleFilePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let {
            Log.d("FilePicker", "Selected file: $it")
            onFilesSelected(listOf(it))
            onDismiss()
        }
    }
    
    Column {
        // Selection Mode
        Text(
            text = "Selection Mode",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row {
            SelectionMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .selectable(
                            selected = selectionMode == mode,
                            onClick = { selectionMode = mode }
                        )
                        .padding(end = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectionMode == mode,
                        onClick = { selectionMode = mode }
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(mode.displayName)
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // File Types
        Text(
            text = "File Types",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyColumn(
            modifier = Modifier.height(200.dp)
        ) {
            items(FileType.values()) { fileType ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = selectedFileTypes.contains(fileType),
                            onClick = {
                                selectedFileTypes = if (selectedFileTypes.contains(fileType)) {
                                    selectedFileTypes - fileType
                                } else {
                                    selectedFileTypes + fileType
                                }
                            }
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = selectedFileTypes.contains(fileType),
                        onCheckedChange = { checked ->
                            selectedFileTypes = if (checked) {
                                selectedFileTypes + fileType
                            } else {
                                selectedFileTypes - fileType
                            }
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.InsertDriveFile,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(fileType.displayName)
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Action Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
            Spacer(modifier = Modifier.width(8.dp))
            Button(
                onClick = {
                    val mimeTypes = selectedFileTypes.flatMap { it.mimeTypes }.toTypedArray()
                    if (mimeTypes.isNotEmpty()) {
                        if (selectionMode == SelectionMode.MULTIPLE) {
                            filePickerLauncher.launch(mimeTypes)
                        } else {
                            singleFilePickerLauncher.launch(mimeTypes)
                        }
                    }
                },
                enabled = selectedFileTypes.isNotEmpty()
            ) {
                Text("Select Files")
            }
        }
    }
}

@Composable
private fun DirectorySelectionTab(
    onDirectorySelected: (Uri) -> Unit,
    onDismiss: () -> Unit
) {
    val directoryPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let {
            Log.d("FilePicker", "Selected directory: $it")
            onDirectorySelected(it)
            onDismiss()
        }
    }
    
    Column {
        Text(
            text = "Select a directory to access all files within it.",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Folder,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Choose Directory",
                style = MaterialTheme.typography.titleMedium
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Action Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
            Spacer(modifier = Modifier.width(8.dp))
            Button(
                onClick = { directoryPickerLauncher.launch(null) }
            ) {
                Text("Select Directory")
            }
        }
    }
}

private enum class SelectionMode(val displayName: String) {
    SINGLE("Single File"),
    MULTIPLE("Multiple Files")
}

package com.compress.my.ui.components

import android.os.Environment
import android.util.Log
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.filled.FolderOpen
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.SdCard
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

@Composable
fun DirectoryBrowser(
    onDirectorySelected: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    var currentPath by remember { mutableStateOf(Environment.getExternalStorageDirectory().absolutePath) }
    var directories by remember { mutableStateOf<List<DirectoryItem>>(emptyList()) }
    var isLoading by remember { mutableStateOf(false) }
    var error by remember { mutableStateOf<String?>(null) }
    
    LaunchedEffect(currentPath) {
        loadDirectories(currentPath) { result ->
            when (result) {
                is DirectoryResult.Success -> {
                    directories = result.directories
                    error = null
                }
                is DirectoryResult.Error -> {
                    error = result.message
                    directories = emptyList()
                }
            }
            isLoading = false
        }
    }
    
    Column(modifier = modifier) {
        // Header with current path and navigation
        DirectoryHeader(
            currentPath = currentPath,
            onNavigateUp = {
                val parentFile = File(currentPath).parentFile
                if (parentFile != null && parentFile.canRead()) {
                    currentPath = parentFile.absolutePath
                }
            },
            onNavigateToRoot = {
                currentPath = Environment.getExternalStorageDirectory().absolutePath
            }
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Directory list
        when {
            isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            error != null -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Error: $error",
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(directories) { directory ->
                        DirectoryItem(
                            directory = directory,
                            onDirectoryClick = { path ->
                                currentPath = path
                            },
                            onDirectorySelect = { path ->
                                Log.d("DirectoryBrowser", "Selected directory: $path")
                                onDirectorySelected(path)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DirectoryHeader(
    currentPath: String,
    onNavigateUp: () -> Unit,
    onNavigateToRoot: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = onNavigateUp,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Navigate up",
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            IconButton(
                onClick = onNavigateToRoot,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = "Go to root",
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = currentPath,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun DirectoryItem(
    directory: DirectoryItem,
    onDirectoryClick: (String) -> Unit,
    onDirectorySelect: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onDirectoryClick(directory.path) },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Surface(
                modifier = Modifier.size(40.dp),
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.primaryContainer
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = if (directory.isSpecial) Icons.Default.SdCard else Icons.Default.Folder,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = directory.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                if (directory.itemCount >= 0) {
                    Text(
                        text = "${directory.itemCount} items",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Surface(
                modifier = Modifier
                    .clickable { onDirectorySelect(directory.path) },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.primary
            ) {
                Text(
                    text = "Select",
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

private suspend fun loadDirectories(
    path: String,
    onResult: (DirectoryResult) -> Unit
) {
    withContext(Dispatchers.IO) {
        try {
            val directory = File(path)
            if (!directory.exists() || !directory.isDirectory || !directory.canRead()) {
                onResult(DirectoryResult.Error("Cannot access directory"))
                return@withContext
            }
            
            val files = directory.listFiles()?.filter { it.isDirectory && it.canRead() }
                ?: emptyList()
            
            val directoryItems = files.map { file ->
                val itemCount = try {
                    file.listFiles()?.size ?: -1
                } catch (e: SecurityException) {
                    -1
                }
                
                DirectoryItem(
                    name = file.name,
                    path = file.absolutePath,
                    itemCount = itemCount,
                    isSpecial = isSpecialDirectory(file)
                )
            }.sortedBy { it.name.lowercase() }
            
            onResult(DirectoryResult.Success(directoryItems))
        } catch (e: Exception) {
            onResult(DirectoryResult.Error(e.message ?: "Unknown error"))
        }
    }
}

private fun isSpecialDirectory(file: File): Boolean {
    val specialDirs = listOf("Android", "DCIM", "Download", "Documents", "Pictures", "Movies", "Music")
    return specialDirs.contains(file.name)
}

data class DirectoryItem(
    val name: String,
    val path: String,
    val itemCount: Int,
    val isSpecial: Boolean = false
)

sealed class DirectoryResult {
    data class Success(val directories: List<DirectoryItem>) : DirectoryResult()
    data class Error(val message: String) : DirectoryResult()
}

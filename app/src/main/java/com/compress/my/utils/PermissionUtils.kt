package com.compress.my.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.core.content.ContextCompat

object PermissionUtils {
    
    /**
     * Get the required storage permissions based on Android API level
     */
    fun getRequiredStoragePermissions(): List<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ (API 33+) - Granular media permissions
                listOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_AUDIO
                )
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // Android 11+ (API 30+) - Scoped storage
                listOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            else -> {
                // Android 10 and below
                listOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }
    
    /**
     * Check if all required storage permissions are granted
     */
    fun hasStoragePermissions(context: Context): Boolean {
        val requiredPermissions = getRequiredStoragePermissions()
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Check if MANAGE_EXTERNAL_STORAGE permission is granted (Android 11+)
     */
    fun hasManageExternalStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            true // Not needed for older versions
        }
    }
    
    /**
     * Get intent to request MANAGE_EXTERNAL_STORAGE permission
     */
    fun getManageExternalStorageIntent(packageName: String): Intent {
        return Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
            data = Uri.parse("package:$packageName")
        }
    }
    
    /**
     * Check if we have all necessary permissions for full storage access
     */
    fun hasFullStorageAccess(context: Context): Boolean {
        return hasStoragePermissions(context) && hasManageExternalStoragePermission()
    }
    
    /**
     * Get permission status description for UI
     */
    fun getPermissionStatusDescription(context: Context): String {
        val hasBasicPermissions = hasStoragePermissions(context)
        val hasManagePermission = hasManageExternalStoragePermission()
        
        return when {
            hasBasicPermissions && hasManagePermission -> "All storage permissions granted"
            hasBasicPermissions && !hasManagePermission -> "Basic permissions granted, full access needed"
            !hasBasicPermissions -> "Storage permissions required"
            else -> "Unknown permission state"
        }
    }
}

/**
 * File type categories for filtering
 */
enum class FileType(val displayName: String, val mimeTypes: List<String>, val extensions: List<String>) {
    IMAGES(
        displayName = "Images",
        mimeTypes = listOf("image/*"),
        extensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "svg")
    ),
    VIDEOS(
        displayName = "Videos", 
        mimeTypes = listOf("video/*"),
        extensions = listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v")
    ),
    AUDIO(
        displayName = "Audio",
        mimeTypes = listOf("audio/*"),
        extensions = listOf("mp3", "wav", "flac", "aac", "ogg", "m4a", "wma")
    ),
    DOCUMENTS(
        displayName = "Documents",
        mimeTypes = listOf("application/pdf", "application/msword", "text/*"),
        extensions = listOf("pdf", "doc", "docx", "txt", "rtf", "odt", "xls", "xlsx", "ppt", "pptx")
    ),
    ARCHIVES(
        displayName = "Archives",
        mimeTypes = listOf("application/zip", "application/x-rar-compressed"),
        extensions = listOf("zip", "rar", "7z", "tar", "gz", "bz2", "xz")
    ),
    ALL_FILES(
        displayName = "All Files",
        mimeTypes = listOf("*/*"),
        extensions = emptyList()
    )
}

package com.compress.my.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.compress.my.ui.foryou.ForYouRoute
import com.compress.my.ui.interests.InterestsRoute
import com.compress.my.ui.saved.SavedRoute

/**
 * Top-level navigation graph. Navigation is organized as explained at
 * https://d.android.com/jetpack/compose/nav-adaptive
 *
 * The navigation graph defined in this file defines the different top level routes. Navigation
 * within each route is handled using state and Back Handlers.
 */
@Composable
fun CompressNavHost(
    navController: NavHostController,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    startDestination: String = TopLevelDestination.FOR_YOU.name,
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
    ) {
        composable(TopLevelDestination.FOR_YOU.name) {
            ForYouRoute(onBackClick = onBackClick)
        }
        composable(TopLevelDestination.SAVED.name) {
            SavedRoute(onBackClick = onBackClick)
        }
        composable(TopLevelDestination.INTERESTS.name) {
            InterestsRoute(onBackClick = onBackClick)
        }
    }
}
